{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Carkit%5C%5CDesktop%5C%5Ccookies%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Carkit%5C%5CDesktop%5C%5Ccookies%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Carkit%5C%5CDesktop%5C%5Ccookies%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cfooter%5C%5CFooterComponent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Carkit%5C%5CDesktop%5C%5Ccookies%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-toggle.tsx%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&server=false!"]}