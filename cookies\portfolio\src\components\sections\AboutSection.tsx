import Image from "next/image";

export default function AboutSection() {
  return (
    <section className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Left Content - About Text */}
          <div className="space-y-6">
            <div className="relative">
              <h2 className="text-4xl lg:text-5xl font-bold text-deep-charcoal dark:text-dark-text mb-8">
                Who Am I?
              </h2>
              {/* Character image positioned on the text */}
              <div className="absolute -top-4 -right-8 lg:-right-16 w-24 h-24 lg:w-32 lg:h-32">
                <Image
                  src="/whoami-character.png"
                  alt="Character with cat illustration"
                  fill
                  className="object-contain"
                />
              </div>
            </div>

            <div className="space-y-4 text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed">
              <p className="text-lg">
                I'm a passionate Full Stack Engineer with a love for creating meaningful software solutions.
                When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects,
                or spending quality time with my furry companion.
              </p>

              <p className="text-lg">
                My journey in software development started with curiosity and has evolved into a mission to build
                tools that make developers' lives easier and more productive. I believe in the power of clean code,
                thoughtful design, and collaborative development.
              </p>

              <p className="text-lg">
                Beyond the technical realm, I enjoy writing about my experiences, sharing knowledge with the
                community, and mentoring aspiring developers. Every project is an opportunity to learn something
                new and push the boundaries of what's possible.
              </p>
            </div>

            {/* Skills/Interests */}
            <div className="mt-8">
              <h3 className="text-xl font-semibold text-deep-charcoal dark:text-dark-text mb-4">
                What I Love Working With
              </h3>
              <div className="flex flex-wrap gap-3">
                {[
                  'React', 'Next.js', 'TypeScript', 'Node.js',
                  'Python', 'PostgreSQL', 'Docker', 'AWS',
                  'Open Source', 'Technical Writing'
                ].map((skill) => (
                  <span
                    key={skill}
                    className="px-4 py-2 bg-accent-green/10 text-accent-green rounded-full text-sm font-medium border border-accent-green/20"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          </div>

        {/* Right Content - Additional Visual Element */}
        <div className="relative">
          <div className="bg-light-almond/50 dark:bg-dark-surface/50 rounded-2xl p-8 lg:p-12">
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-accent-green rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-deep-charcoal dark:text-dark-text">Problem Solver</h4>
                  <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                    I love tackling complex challenges and finding elegant solutions
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-accent-green rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-deep-charcoal dark:text-dark-text">Continuous Learner</h4>
                  <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                    Always exploring new technologies and best practices
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-accent-green rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2.01.99L12 11l-1.99-2.01A2.5 2.5 0 0 0 8 8H5.46c-.8 0-1.54.37-2.01.99L1 12v10h2v-6h2.5l-1.5-4.5h2L8 18h8l2-6.5h2L18.5 16H21v6h2z"/>
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-deep-charcoal dark:text-dark-text">Team Player</h4>
                  <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                    Collaboration and knowledge sharing drive the best results
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </section>
  );
}
