export default function ContributionSection() {
  return (
    <section className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="bg-deep-charcoal dark:bg-dark-surface rounded-2xl p-8 lg:p-12">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl lg:text-4xl font-bold text-light-almond dark:text-dark-text">
            Contribution Graph
          </h2>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm text-light-almond/70 dark:text-dark-text/70 mb-1">
                167 contributions in the last year
              </div>
              <div className="flex items-center space-x-2 text-xs text-light-almond/50 dark:text-dark-text/50">
                <span>Less</span>
                <div className="flex space-x-1">
                  <div className="w-3 h-3 bg-light-almond/20 dark:bg-dark-text/20 rounded-sm"></div>
                  <div className="w-3 h-3 bg-accent-green/30 rounded-sm"></div>
                  <div className="w-3 h-3 bg-accent-green/60 rounded-sm"></div>
                  <div className="w-3 h-3 bg-accent-green rounded-sm"></div>
                </div>
                <span>More</span>
              </div>
            </div>
            <div className="text-right space-y-1">
              <div className="text-sm font-medium text-accent-green bg-accent-green/20 px-2 py-1 rounded">2025</div>
              <div className="text-sm text-light-almond/70 dark:text-dark-text/70">2024</div>
              <div className="text-sm text-light-almond/70 dark:text-dark-text/70">2023</div>
              <div className="text-sm text-light-almond/70 dark:text-dark-text/70">2022</div>
              <div className="text-sm text-light-almond/70 dark:text-dark-text/70">2021</div>
            </div>
          </div>
        </div>

        {/* Month Labels */}
        <div className="mb-4">
          <div className="grid grid-cols-12 gap-2 text-xs text-light-almond/50 dark:text-dark-text/50 mb-2">
            <div>Jul</div>
            <div>Aug</div>
            <div>Sep</div>
            <div>Oct</div>
            <div>Nov</div>
            <div>Dec</div>
            <div>Jan</div>
            <div>Feb</div>
            <div>Mar</div>
            <div>Apr</div>
            <div>May</div>
            <div>Jun</div>
          </div>
        </div>

        {/* Contribution Grid */}
        <div className="overflow-x-auto">
          <div className="grid grid-cols-53 gap-1 min-w-[800px]">
            {/* Generate contribution squares for a full year */}
            {Array.from({ length: 371 }, (_, i) => {
              // Create a more realistic pattern
              const weekday = i % 7;
              const isWeekend = weekday === 0 || weekday === 6;
              const monthProgress = (i / 371) * 12;

              // Higher activity in certain periods
              const isHighActivityPeriod =
                (monthProgress >= 2 && monthProgress <= 4) || // Mar-May (high activity)
                (monthProgress >= 8 && monthProgress <= 10);  // Sep-Nov (high activity)

              let intensity = 0;
              const random = Math.random();

              if (isWeekend) {
                // Lower activity on weekends
                if (random > 0.8) intensity = 1;
                else if (random > 0.9) intensity = 2;
              } else if (isHighActivityPeriod) {
                // Higher activity during active periods
                if (random > 0.3) intensity = 1;
                if (random > 0.6) intensity = 2;
                if (random > 0.8) intensity = 3;
                if (random > 0.95) intensity = 4;
              } else {
                // Normal activity
                if (random > 0.5) intensity = 1;
                if (random > 0.75) intensity = 2;
                if (random > 0.9) intensity = 3;
              }

              const getIntensityClass = (level: number) => {
                switch (level) {
                  case 0: return 'bg-light-almond/20 dark:bg-dark-text/20';
                  case 1: return 'bg-accent-green/30';
                  case 2: return 'bg-accent-green/60';
                  case 3: return 'bg-accent-green/80';
                  case 4: return 'bg-accent-green';
                  default: return 'bg-light-almond/20 dark:bg-dark-text/20';
                }
              };

              return (
                <div
                  key={i}
                  className={`w-3 h-3 rounded-sm ${getIntensityClass(intensity)} hover:ring-1 hover:ring-accent-green transition-all cursor-pointer`}
                  title={`${intensity} contributions on day ${i + 1}`}
                />
              );
            })}
          </div>
        </div>

        {/* GitHub Link */}
        <div className="mt-8 pt-6 border-t border-light-almond/10 dark:border-dark-text/10">
          <a
            href="https://github.com/sumeetvishwakarma"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center space-x-2 text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            <span>View on GitHub</span>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </a>
        </div>
        </div>
      </div>
    </section>
  );
}
